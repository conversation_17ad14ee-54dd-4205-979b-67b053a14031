# Nexed Web Application Configuration
# This file contains default configuration values for the application
# Values can be overridden by environment variables or runtime configuration

# Application Information
app:
  name: "Nexed Web"
  version: "1.0.0"
  description: "Professional Enterprise Web Application"
  environment: "development" # development, staging, production

# API Configuration
api:
  baseUrl: "/api"
  timeout: 30000 # 30 seconds
  retries: 3
  retryDelay: 1000 # 1 second

# Feature Flags
features:
  # Core Features
  enableAnalytics: true
  enableNotifications: true
  enableDarkMode: true
  enableRealTimeUpdates: true
  
  # Advanced Features
  enableAdvancedSearch: true
  enableBetaFeatures: false
  enableExperimentalUI: false
  enableDebugMode: false
  
  # Enterprise Features
  enableSSOLogin: false
  enableAuditLogging: true
  enableDataExport: true
  enableAdvancedReporting: false

# User Interface Settings
ui:
  # Theme Configuration
  defaultTheme: "system" # light, dark, system
  allowThemeToggle: true
  
  # Layout Settings
  sidebarCollapsed: false
  compactMode: false
  showTooltips: true
  animationsEnabled: true
  reducedMotion: false
  highContrastMode: false
  
  # Data Display
  itemsPerPage: 25
  maxItemsPerPage: 100
  enableVirtualScrolling: true
  
  # Accessibility
  enableScreenReader: true
  enableKeyboardNavigation: true
  focusIndicatorVisible: true

# Performance Settings
performance:
  # Caching
  enableCaching: true
  cacheTimeout: 300000 # 5 minutes
  
  # Loading
  enableLazyLoading: true
  enableCodeSplitting: true
  preloadCriticalResources: true
  
  # Optimization
  enableImageOptimization: true
  enableAssetCompression: true

# Security Settings
security:
  # Session Management
  sessionTimeout: 3600000 # 1 hour
  enableAutoLogout: true
  maxLoginAttempts: 5
  lockoutDuration: 900000 # 15 minutes
  
  # Content Security
  enableCSP: true
  enableXSSProtection: true
  enableClickjacking: true
  
  # Data Protection
  enableDataEncryption: true
  enableSecureHeaders: true

# File Upload Settings
uploads:
  maxFileSize: 10485760 # 10MB
  allowedTypes:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "application/pdf"
    - "text/csv"
    - "application/vnd.ms-excel"
    - "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  enableVirusScanning: false
  enableImageResizing: true

# Localization Settings
localization:
  defaultLanguage: "en"
  supportedLanguages:
    - "en"
    - "es"
    - "fr"
    - "de"
  enableRTL: false
  dateFormat: "MM/dd/yyyy"
  timeFormat: "12h" # 12h, 24h
  timezone: "UTC"
  numberFormat: "en-US"
  currencyFormat: "USD"

# Notification Settings
notifications:
  # Types
  enableToast: true
  enableEmail: false
  enablePush: false
  enableInApp: true
  
  # Behavior
  defaultDuration: 5000 # 5 seconds
  maxNotifications: 5
  enableSound: true
  enableVibration: false
  
  # Positioning
  position: "top-right" # top-left, top-right, bottom-left, bottom-right

# Analytics Settings
analytics:
  enableTracking: false
  provider: "none" # google, adobe, custom, none
  trackPageViews: true
  trackUserInteractions: true
  trackErrors: true
  enableHeatmaps: false
  enableSessionRecording: false

# Development Settings
development:
  enableHotReload: true
  enableSourceMaps: true
  enableProfiling: false
  enableStorybook: true
  enableTestMode: false
  mockApiResponses: false

# Logging Settings
logging:
  level: "info" # error, warn, info, debug
  enableConsoleLogging: true
  enableFileLogging: false
  enableRemoteLogging: false
  maxLogSize: 10485760 # 10MB
  logRetentionDays: 30

# Integration Settings
integrations:
  # External Services
  enableGoogleMaps: false
  enableStripePayments: false
  enableSlackNotifications: false
  enableZendeskSupport: false
  
  # Social Login
  enableGoogleLogin: false
  enableMicrosoftLogin: false
  enableGitHubLogin: false

# Backup and Recovery
backup:
  enableAutoBackup: false
  backupInterval: 86400000 # 24 hours
  retentionPeriod: ********** # 30 days
  enableCloudBackup: false

# Monitoring and Health
monitoring:
  enableHealthChecks: true
  healthCheckInterval: 60000 # 1 minute
  enablePerformanceMonitoring: false
  enableErrorTracking: false
  enableUptimeMonitoring: false
