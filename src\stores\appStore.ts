import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface FeatureFlags {
  enableAnalytics: boolean;
  enableNotifications: boolean;
  enableDarkMode: boolean;
  enableBetaFeatures: boolean;
  enableAdvancedSearch: boolean;
  enableRealTimeUpdates: boolean;
  enableExperimentalUI: boolean;
  enableDebugMode: boolean;
}

export interface AppConfiguration {
  appName: string;
  version: string;
  environment: 'development' | 'staging' | 'production';
  apiBaseUrl: string;
  maxFileUploadSize: number;
  sessionTimeout: number;
  defaultLanguage: string;
  supportedLanguages: string[];
  dateFormat: string;
  timeFormat: string;
  timezone: string;
  itemsPerPage: number;
  maxRetries: number;
  retryDelay: number;
}

export interface AppSettings {
  sidebarCollapsed: boolean;
  compactMode: boolean;
  showTooltips: boolean;
  autoSave: boolean;
  autoSaveInterval: number;
  soundEnabled: boolean;
  animationsEnabled: boolean;
  highContrastMode: boolean;
  reducedMotion: boolean;
}

interface AppState {
  isLoading: boolean;
  isInitialized: boolean;
  featureFlags: FeatureFlags;
  configuration: AppConfiguration;
  settings: AppSettings;

  // Actions
  setLoading: (loading: boolean) => void;
  setInitialized: (initialized: boolean) => void;
  updateFeatureFlag: (flag: keyof FeatureFlags, value: boolean) => void;
  updateFeatureFlags: (flags: Partial<FeatureFlags>) => void;
  updateConfiguration: (config: Partial<AppConfiguration>) => void;
  updateSettings: (settings: Partial<AppSettings>) => void;
  resetToDefaults: () => void;
  loadFromConfig: (config: Partial<AppState>) => void;
  isFeatureEnabled: (flag: keyof FeatureFlags) => boolean;
}

// Default values
const defaultFeatureFlags: FeatureFlags = {
  enableAnalytics: true,
  enableNotifications: true,
  enableDarkMode: true,
  enableBetaFeatures: false,
  enableAdvancedSearch: true,
  enableRealTimeUpdates: true,
  enableExperimentalUI: false,
  enableDebugMode: false,
};

const defaultConfiguration: AppConfiguration = {
  appName: 'Nexed Web',
  version: '1.0.0',
  environment: 'development',
  apiBaseUrl: '/api',
  maxFileUploadSize: 10485760, // 10MB
  sessionTimeout: 3600000, // 1 hour in milliseconds
  defaultLanguage: 'en',
  supportedLanguages: ['en', 'es', 'fr', 'de'],
  dateFormat: 'MM/dd/yyyy',
  timeFormat: '12h',
  timezone: 'UTC',
  itemsPerPage: 25,
  maxRetries: 3,
  retryDelay: 1000,
};

const defaultSettings: AppSettings = {
  sidebarCollapsed: false,
  compactMode: false,
  showTooltips: true,
  autoSave: true,
  autoSaveInterval: 30000, // 30 seconds
  soundEnabled: true,
  animationsEnabled: true,
  highContrastMode: false,
  reducedMotion: false,
};

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        isLoading: false,
        isInitialized: false,
        featureFlags: defaultFeatureFlags,
        configuration: defaultConfiguration,
        settings: defaultSettings,

        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        setInitialized: (initialized: boolean) => {
          set({ isInitialized: initialized });
        },

        updateFeatureFlag: (flag: keyof FeatureFlags, value: boolean) => {
          set(state => ({
            featureFlags: {
              ...state.featureFlags,
              [flag]: value,
            },
          }));
        },

        updateFeatureFlags: (flags: Partial<FeatureFlags>) => {
          set(state => ({
            featureFlags: {
              ...state.featureFlags,
              ...flags,
            },
          }));
        },

        updateConfiguration: (config: Partial<AppConfiguration>) => {
          set(state => ({
            configuration: {
              ...state.configuration,
              ...config,
            },
          }));
        },

        updateSettings: (settings: Partial<AppSettings>) => {
          set(state => ({
            settings: {
              ...state.settings,
              ...settings,
            },
          }));
        },

        resetToDefaults: () => {
          set({
            featureFlags: defaultFeatureFlags,
            configuration: defaultConfiguration,
            settings: defaultSettings,
          });
        },

        loadFromConfig: (config: Partial<AppState>) => {
          set(state => ({
            ...state,
            ...config,
          }));
        },

        isFeatureEnabled: (flag: keyof FeatureFlags) => {
          return get().featureFlags[flag];
        },
      }),
      {
        name: 'app-store',
        partialize: state => ({
          featureFlags: state.featureFlags,
          settings: state.settings,
          // Don't persist configuration as it should come from config file
        }),
      }
    ),
    {
      name: 'app-store',
    }
  )
);

// Helper function to merge configuration from external source
export const mergeAppConfiguration = (
  externalConfig: Partial<AppState>
): Partial<AppState> => {
  return {
    ...externalConfig,
    featureFlags: {
      ...defaultFeatureFlags,
      ...externalConfig.featureFlags,
    },
    configuration: {
      ...defaultConfiguration,
      ...externalConfig.configuration,
    },
    settings: {
      ...defaultSettings,
      ...externalConfig.settings,
    },
  };
};
