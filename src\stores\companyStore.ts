import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface Company {
  id: string;
  name: string;
  logo?: string;
  domain?: string;
  isActive: boolean;
  settings?: {
    currency: string;
    timezone: string;
    language: string;
    dateFormat: string;
  };
}

interface CompanyState {
  companies: Company[];
  currentCompany: Company | null;
  isLoading: boolean;
  error?: string;

  // Actions
  setCompanies: (companies: Company[]) => void;
  setCurrentCompany: (company: Company) => void;
  addCompany: (company: Omit<Company, 'id'>) => void;
  updateCompany: (id: string, updates: Partial<Company>) => void;
  removeCompany: (id: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error?: string) => void;
  clearError: () => void;
  
  // Utility functions
  getCompanyById: (id: string) => Company | undefined;
  getActiveCompanies: () => Company[];
  switchCompany: (companyId: string) => void;
}

// Mock companies for development
const mockCompanies: Company[] = [
  {
    id: 'company-1',
    name: 'Acme Corporation',
    logo: '🏢',
    domain: 'acme.com',
    isActive: true,
    settings: {
      currency: 'USD',
      timezone: 'America/New_York',
      language: 'en',
      dateFormat: 'MM/dd/yyyy',
    },
  },
  {
    id: 'company-2',
    name: 'TechStart Inc.',
    logo: '🚀',
    domain: 'techstart.com',
    isActive: true,
    settings: {
      currency: 'EUR',
      timezone: 'Europe/London',
      language: 'en',
      dateFormat: 'dd/MM/yyyy',
    },
  },
  {
    id: 'company-3',
    name: 'Global Solutions Ltd.',
    logo: '🌍',
    domain: 'globalsolutions.com',
    isActive: true,
    settings: {
      currency: 'GBP',
      timezone: 'Europe/London',
      language: 'en',
      dateFormat: 'dd/MM/yyyy',
    },
  },
];

export const useCompanyStore = create<CompanyState>()(
  devtools(
    persist(
      (set, get) => ({
        companies: mockCompanies,
        currentCompany: mockCompanies[0],
        isLoading: false,
        error: undefined,

        setCompanies: (companies: Company[]) => {
          set({ companies });
        },

        setCurrentCompany: (company: Company) => {
          set({ currentCompany: company });
        },

        addCompany: (companyData: Omit<Company, 'id'>) => {
          const newCompany: Company = {
            ...companyData,
            id: `company-${Date.now()}`,
          };
          set(state => ({
            companies: [...state.companies, newCompany],
          }));
        },

        updateCompany: (id: string, updates: Partial<Company>) => {
          set(state => ({
            companies: state.companies.map(company =>
              company.id === id ? { ...company, ...updates } : company
            ),
            currentCompany:
              state.currentCompany?.id === id
                ? { ...state.currentCompany, ...updates }
                : state.currentCompany,
          }));
        },

        removeCompany: (id: string) => {
          set(state => {
            const filteredCompanies = state.companies.filter(
              company => company.id !== id
            );
            const newCurrentCompany =
              state.currentCompany?.id === id
                ? filteredCompanies[0] || null
                : state.currentCompany;

            return {
              companies: filteredCompanies,
              currentCompany: newCurrentCompany,
            };
          });
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        setError: (error?: string) => {
          set({ error });
        },

        clearError: () => {
          set({ error: undefined });
        },

        getCompanyById: (id: string) => {
          return get().companies.find(company => company.id === id);
        },

        getActiveCompanies: () => {
          return get().companies.filter(company => company.isActive);
        },

        switchCompany: (companyId: string) => {
          const company = get().getCompanyById(companyId);
          if (company) {
            get().setCurrentCompany(company);
          }
        },
      }),
      {
        name: 'company-store',
        partialize: state => ({
          currentCompany: state.currentCompany,
          companies: state.companies,
        }),
      }
    ),
    {
      name: 'company-store',
    }
  )
);

// Helper function to get current company
export const getCurrentCompany = () => {
  return useCompanyStore.getState().currentCompany;
};

// Helper function to switch company
export const switchToCompany = (companyId: string) => {
  useCompanyStore.getState().switchCompany(companyId);
};
