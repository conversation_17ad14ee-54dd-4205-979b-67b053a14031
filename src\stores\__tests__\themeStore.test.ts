import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useThemeStore } from '../themeStore';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

describe('themeStore', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset store state
    useThemeStore.setState({
      theme: 'system',
      colors: {
        primary: '#3b82f6',
        primaryForeground: '#ffffff',
        secondary: '#6366f1',
        secondaryForeground: '#ffffff',
        accent: '#8b5cf6',
        accentForeground: '#ffffff',
        neutral: '#64748b',
        success: '#059669',
        successForeground: '#ffffff',
        warning: '#d97706',
        warningForeground: '#ffffff',
        error: '#dc2626',
        errorForeground: '#ffffff',
        info: '#0ea5e9',
        infoForeground: '#ffffff',
        background: '#ffffff',
        foreground: '#111827',
        surface: '#f8fafc',
        surfaceSecondary: '#f1f5f9',
        surfaceTertiary: '#e5e7eb',
        text: '#0f172a',
        textSecondary: '#64748b',
        textMuted: '#94a3b8',
        textInverse: '#ffffff',
        border: '#e2e8f0',
        borderSecondary: '#d1d5db',
        borderFocus: '#2563eb',
        hover: '#f1f5f9',
        active: '#e2e8f0',
        focus: '#dbeafe',
        disabled: '#f8fafc',
        disabledForeground: '#cbd5e1',
        muted: '#f1f5f9',
        mutedForeground: '#64748b',
        destructive: '#dc2626',
        destructiveForeground: '#ffffff',
        ring: '#2563eb',
        input: '#ffffff',
        inputForeground: '#111827',
        card: '#ffffff',
        cardForeground: '#111827',
        popover: '#ffffff',
        popoverForeground: '#111827',
        shadow: 'rgba(0, 0, 0, 0.1)',
        shadowSecondary: 'rgba(0, 0, 0, 0.05)',
        overlay: 'rgba(0, 0, 0, 0.5)',
        chart1: '#2563eb',
        chart2: '#059669',
        chart3: '#d97706',
        chart4: '#7c3aed',
        chart5: '#dc2626',
      },
    });
  });

  it('initializes with system theme', () => {
    const { theme } = useThemeStore.getState();
    expect(theme).toBe('system');
  });

  it('sets light theme correctly', () => {
    const { setTheme } = useThemeStore.getState();
    setTheme('light');

    const { theme, colors } = useThemeStore.getState();
    expect(theme).toBe('light');
    expect(colors.background).toBe('#ffffff');
    expect(colors.text).toBe('#0f172a');
  });

  it('sets dark theme correctly', () => {
    const { setTheme } = useThemeStore.getState();
    setTheme('dark');

    const { theme, colors } = useThemeStore.getState();
    expect(theme).toBe('dark');
    expect(colors.background).toBe('#0f172a');
    expect(colors.text).toBe('#f8fafc');
  });

  it('toggles between light and dark themes', () => {
    const { setTheme, toggleTheme } = useThemeStore.getState();

    setTheme('light');
    toggleTheme();
    expect(useThemeStore.getState().theme).toBe('dark');

    toggleTheme();
    expect(useThemeStore.getState().theme).toBe('light');
  });
});
