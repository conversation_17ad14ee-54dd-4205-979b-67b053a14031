import { http, HttpResponse } from 'msw';

// Enhanced user data for robust testing
const users = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    createdAt: '2024-01-15T10:30:00Z',
    avatar: '👨‍💼',
    permissions: ['read', 'write', 'delete', 'admin'],
    status: 'active',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    createdAt: '2024-01-16T14:20:00Z',
    avatar: '👤',
    permissions: ['read', 'write'],
    status: 'active',
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'moderator',
    createdAt: '2024-01-17T09:15:00Z',
    avatar: '👮‍♀️',
    permissions: ['read', 'write', 'moderate'],
    status: 'active',
  },
  {
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'guest',
    createdAt: '2024-01-18T11:45:00Z',
    avatar: '👻',
    permissions: ['read'],
    status: 'active',
  },
];

// Development users for testing
const devUsers = [
  {
    id: 'dev-admin',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '👨‍💼',
    permissions: ['read', 'write', 'delete', 'admin'],
    status: 'active',
  },
  {
    id: 'dev-user',
    name: 'Regular User',
    email: '<EMAIL>',
    role: 'user',
    avatar: '👤',
    permissions: ['read', 'write'],
    status: 'active',
  },
  {
    id: 'dev-moderator',
    name: 'Moderator User',
    email: '<EMAIL>',
    role: 'moderator',
    avatar: '👮‍♀️',
    permissions: ['read', 'write', 'moderate'],
    status: 'active',
  },
  {
    id: 'dev-guest',
    name: 'Guest User',
    email: '<EMAIL>',
    role: 'guest',
    avatar: '👻',
    permissions: ['read'],
    status: 'active',
  },
];

// Authentication credentials for testing
const authCredentials = [
  { email: '<EMAIL>', password: 'password', userId: '1' },
  { email: '<EMAIL>', password: 'password', userId: '2' },
  { email: '<EMAIL>', password: 'password', userId: '3' },
  { email: '<EMAIL>', password: 'password', userId: '4' },
  // Dev users
  { email: '<EMAIL>', password: 'dev', userId: 'dev-admin' },
  { email: '<EMAIL>', password: 'dev', userId: 'dev-user' },
  { email: '<EMAIL>', password: 'dev', userId: 'dev-moderator' },
  { email: '<EMAIL>', password: 'dev', userId: 'dev-guest' },
];

// Combine all users for lookup
const allUsers = [...users, ...devUsers];

// Simulate network delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Define request handlers for API mocking
export const handlers = [
  // Get all users
  http.get('/api/users', async () => {
    await delay(500); // Simulate network delay
    return HttpResponse.json(users);
  }),

  // Get single user
  http.get('/api/users/:id', async ({ params }) => {
    await delay(300);
    const user = users.find(u => u.id === params.id);
    if (!user) {
      return HttpResponse.json({ error: 'User not found' }, { status: 404 });
    }
    return HttpResponse.json(user);
  }),

  // Create new user
  http.post('/api/users', async ({ request }) => {
    await delay(800); // Simulate slower create operation

    const newUserData = (await request.json()) as Record<string, unknown>;
    const newUser = {
      id: Date.now().toString(),
      name: newUserData.name as string,
      email: newUserData.email as string,
      role: (newUserData.role as string) || 'user',
      createdAt: new Date().toISOString(),
      avatar: '👤',
      permissions: ['read', 'write'],
      status: 'active',
    };

    users.push(newUser);
    return HttpResponse.json(newUser, { status: 201 });
  }),

  // Update user
  http.put('/api/users/:id', async ({ params, request }) => {
    await delay(600);

    const userIndex = users.findIndex(u => u.id === params.id);
    if (userIndex === -1) {
      return HttpResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const updateData = (await request.json()) as Record<string, unknown>;
    users[userIndex] = { ...users[userIndex], ...updateData };

    return HttpResponse.json(users[userIndex]);
  }),

  // Delete user
  http.delete('/api/users/:id', async ({ params }) => {
    await delay(400);

    const userIndex = users.findIndex(u => u.id === params.id);
    if (userIndex === -1) {
      return HttpResponse.json({ error: 'User not found' }, { status: 404 });
    }

    users.splice(userIndex, 1);
    return HttpResponse.json({ success: true });
  }),

  // Get current user profile
  http.get('/api/user', async () => {
    await delay(200);
    return HttpResponse.json({
      id: 'current-user',
      name: 'Current User',
      email: '<EMAIL>',
      role: 'admin',
      avatar: null,
      preferences: {
        theme: 'system',
        notifications: true,
      },
    });
  }),

  // Example error response
  http.get('/api/error', () => {
    return HttpResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }),

  // Simulate authentication endpoints
  http.post('/api/auth/login', async ({ request }) => {
    await delay(1000);
    const credentials = (await request.json()) as Record<string, unknown>;

    // Enhanced authentication with multiple user types
    const authUser = authCredentials.find(
      cred =>
        cred.email === credentials.email &&
        cred.password === credentials.password
    );

    if (authUser) {
      const user = allUsers.find(u => u.id === authUser.userId);
      if (user) {
        return HttpResponse.json({
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            avatar: user.avatar,
            permissions: user.permissions,
            status: user.status,
          },
          token: `mock-jwt-token-${user.id}`,
          expiresIn: 3600, // 1 hour
        });
      }
    }

    return HttpResponse.json({ error: 'Invalid credentials' }, { status: 401 });
  }),

  // Dev login endpoint for development users
  http.post('/api/auth/dev-login', async ({ request }) => {
    await delay(500);
    const { userId } = (await request.json()) as Record<string, unknown>;

    const user = devUsers.find(u => u.id === userId);
    if (user) {
      return HttpResponse.json({
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          avatar: user.avatar,
          permissions: user.permissions,
          status: user.status,
        },
        token: `mock-jwt-token-${user.id}`,
        expiresIn: 3600,
        isDev: true,
      });
    }

    return HttpResponse.json({ error: 'User not found' }, { status: 404 });
  }),

  // Logout endpoint
  http.post('/api/auth/logout', async () => {
    await delay(200);
    return HttpResponse.json({ success: true });
  }),

  // Get current user profile
  http.get('/api/auth/me', async ({ request }) => {
    await delay(300);
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Extract user ID from token (mock implementation)
    const token = authHeader.replace('Bearer ', '');
    const userId = token.replace('mock-jwt-token-', '');

    const user = allUsers.find(u => u.id === userId);
    if (user) {
      return HttpResponse.json({
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          avatar: user.avatar,
          permissions: user.permissions,
          status: user.status,
        },
      });
    }

    return HttpResponse.json({ error: 'User not found' }, { status: 404 });
  }),

  // Check user permissions
  http.get('/api/auth/permissions', async ({ request }) => {
    await delay(200);
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const userId = token.replace('mock-jwt-token-', '');

    const user = allUsers.find(u => u.id === userId);
    if (user) {
      return HttpResponse.json({
        permissions: user.permissions,
        role: user.role,
      });
    }

    return HttpResponse.json({ error: 'User not found' }, { status: 404 });
  }),

  // Health check endpoint
  http.get('/api/health', () => {
    return HttpResponse.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    });
  }),
];
