import React from 'react';
import { useCompanyStore } from '../../../stores/companyStore';
import { useThemeStore } from '../../../stores/themeStore';
import Dropdown from '../Dropdown/Dropdown';
import { BuildingIcon, SwitchIcon, ChevronDownIcon, CheckIcon } from '../../icons';
import type { DropdownItem } from '../Dropdown/Dropdown';

export interface CompanySelectorProps {
  className?: string;
  showLabel?: boolean;
  'data-testid'?: string;
}

const CompanySelector: React.FC<CompanySelectorProps> = ({
  className = '',
  showLabel = false,
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const { companies, currentCompany, switchCompany } = useCompanyStore();

  const handleCompanySwitch = (companyId: string) => {
    switchCompany(companyId);
  };

  // Create dropdown items from companies
  const companyItems: DropdownItem[] = companies.map(company => ({
    id: company.id,
    label: company.name,
    icon: (
      <div className="flex items-center space-x-2">
        <div className="text-lg">{company.logo || <BuildingIcon className="w-4 h-4" />}</div>
        {currentCompany?.id === company.id && (
          <CheckIcon className="w-4 h-4" style={{ color: colors.primary }} />
        )}
      </div>
    ),
    onClick: () => handleCompanySwitch(company.id),
    description: company.domain,
  }));

  // Add divider and switch company option
  const dropdownItems: DropdownItem[] = [
    ...companyItems,
    {
      id: 'divider-1',
      label: '',
      isDivider: true,
    },
    {
      id: 'switch-company',
      label: 'Manage Companies',
      icon: <SwitchIcon className="w-4 h-4" />,
      onClick: () => {
        // TODO: Open company management modal
        console.log('Open company management modal');
      },
      description: 'Add or manage companies',
    },
  ];

  const trigger = (
    <div
      className={`flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-opacity-10 transition-colors duration-200 cursor-pointer ${className}`}
      style={{ backgroundColor: 'transparent' }}
    >
      {/* Company Logo/Icon */}
      <div className="flex-shrink-0">
        {currentCompany?.logo ? (
          <div className="text-lg">{currentCompany.logo}</div>
        ) : (
          <BuildingIcon className="w-5 h-5" style={{ color: colors.text }} />
        )}
      </div>

      {/* Company Name */}
      {showLabel && currentCompany && (
        <div className="flex-1 min-w-0">
          <div
            className="text-sm font-medium truncate"
            style={{ color: colors.text }}
          >
            {currentCompany.name}
          </div>
        </div>
      )}

      {/* Dropdown Arrow */}
      <ChevronDownIcon 
        className="w-4 h-4 transition-transform duration-200" 
        style={{ color: colors.mutedForeground }} 
      />
    </div>
  );

  return (
    <Dropdown
      trigger={trigger}
      items={dropdownItems}
      align="left"
      className={className}
      data-testid={testId}
    />
  );
};

export default CompanySelector;
