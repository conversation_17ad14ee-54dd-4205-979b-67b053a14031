import type { Meta, StoryObj } from '@storybook/react-vite';
import { ThemeToggle } from './ThemeToggle';

const meta: Meta<typeof ThemeToggle> = {
  title: 'Components/ThemeToggle',
  component: ThemeToggle,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'An animated theme toggle button that cycles between light, dark, and system themes with smooth transitions and accessibility features.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg'],
      description: 'Size of the toggle button',
    },
    showLabel: {
      control: { type: 'boolean' },
      description: 'Whether to show the theme label next to the button',
    },
    className: {
      control: { type: 'text' },
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    size: 'md',
    showLabel: false,
  },
};

export const WithLabel: Story = {
  args: {
    size: 'md',
    showLabel: true,
  },
};

export const Small: Story = {
  args: {
    size: 'sm',
    showLabel: false,
  },
};

export const Large: Story = {
  args: {
    size: 'lg',
    showLabel: false,
  },
};

export const SmallWithLabel: Story = {
  args: {
    size: 'sm',
    showLabel: true,
  },
};

export const LargeWithLabel: Story = {
  args: {
    size: 'lg',
    showLabel: true,
  },
};

// Showcase all sizes together
export const AllSizes: Story = {
  render: () => (
    <div className="flex items-center gap-8">
      <div className="flex flex-col items-center gap-2">
        <ThemeToggle size="sm" />
        <span className="text-xs text-slate-500">Small</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <ThemeToggle size="md" />
        <span className="text-xs text-slate-500">Medium</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <ThemeToggle size="lg" />
        <span className="text-xs text-slate-500">Large</span>
      </div>
    </div>
  ),
};

// Showcase with labels
export const WithLabels: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <ThemeToggle size="sm" showLabel />
      <ThemeToggle size="md" showLabel />
      <ThemeToggle size="lg" showLabel />
    </div>
  ),
};
