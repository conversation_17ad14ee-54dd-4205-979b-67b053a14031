import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface User {
  id: number
  name: string
  email: string
  role: string
  createdAt?: string
}

interface UserState {
  users: User[]
  loading: boolean
  error: string | null
  fetchUsers: () => Promise<void>
  addUser: (user: Omit<User, 'id'>) => Promise<void>
  clearError: () => void
}

export const useUserStore = create<UserState>()(
  devtools(
    (set, _get) => ({
      users: [],
      loading: false,
      error: null,

      fetchUsers: async () => {
        set({ loading: true, error: null })
        try {
          const response = await fetch('/api/users')
          if (!response.ok) {
            throw new Error('Failed to fetch users')
          }
          const users = await response.json()
          set({ users, loading: false })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Unknown error',
            loading: false 
          })
        }
      },

      addUser: async (userData) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch('/api/users', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(userData),
          })
          
          if (!response.ok) {
            throw new Error('Failed to add user')
          }
          
          const newUser = await response.json()
          set(state => ({ 
            users: [...state.users, newUser],
            loading: false 
          }))
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Unknown error',
            loading: false 
          })
        }
      },

      clearError: () => set({ error: null }),
    }),
    {
      name: 'user-store',
    }
  )
)
