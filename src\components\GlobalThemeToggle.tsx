import React, { useState } from 'react';
import { useTheme } from '../hooks/useTheme';
import type { Theme } from '../stores/themeStore';

export interface GlobalThemeToggleProps {
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
  'data-testid'?: string;
}

export function GlobalThemeToggle({
  position = 'top-right',
  size = 'md',
  showLabel = false,
  className = '',
  'data-testid': testId,
}: GlobalThemeToggleProps) {
  const { theme, setTheme, colors } = useTheme();
  const [isAnimating, setIsAnimating] = useState(false);

  const themes: Array<{
    value: Theme;
    label: string;
    icon: React.ComponentType<{ className?: string }>;
    description: string;
  }> = [
    {
      value: 'light',
      label: 'Light Mode',
      description: 'Bright and clean interface',
      icon: ({ className }) => (
        <svg
          className={className}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
      ),
    },
    {
      value: 'dark',
      label: 'Dark Mode',
      description: 'Easy on the eyes',
      icon: ({ className }) => (
        <svg
          className={className}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
          />
        </svg>
      ),
    },
    {
      value: 'system',
      label: 'System',
      description: 'Follow system preference',
      icon: ({ className }) => (
        <svg
          className={className}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
      ),
    },
  ];

  const currentThemeIndex = themes.findIndex(t => t.value === theme);
  const currentTheme = themes[currentThemeIndex];

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  };

  const sizeClasses = {
    sm: 'w-10 h-10',
    md: 'w-12 h-12',
    lg: 'w-14 h-14',
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const handleToggle = async () => {
    if (isAnimating) return;

    setIsAnimating(true);

    // Cycle through themes: light -> dark -> system -> light
    const nextIndex = (currentThemeIndex + 1) % themes.length;
    const nextTheme = themes[nextIndex];

    // Add a small delay for animation effect
    setTimeout(() => {
      setTheme(nextTheme.value);
      setIsAnimating(false);
    }, 150);
  };

  const IconComponent = currentTheme.icon;

  return (
    <div
      className={`fixed z-50 ${positionClasses[position]} ${className}`}
      data-testid={testId}
    >
      <div className="relative">
        {/* Main toggle button */}
        <button
          onClick={handleToggle}
          className={`
            ${sizeClasses[size]}
            rounded-full backdrop-blur-md shadow-lg
            flex items-center justify-center
            transition-all duration-300 ease-out
            hover:scale-110 hover:shadow-xl active:scale-95
            transform-gpu
            ${isAnimating ? 'animate-spin' : ''}
            relative overflow-hidden
            group
          `}
          style={{
            backgroundColor: `${colors.background}95`,
            color: colors.text,
            boxShadow: `0 8px 25px ${colors.shadow}25, 0 0 0 1px ${colors.background}40`,
          }}
          aria-label={`Switch to ${themes[(currentThemeIndex + 1) % themes.length].label}`}
          title={currentTheme.label}
        >
          {/* Animated background gradient */}
          <div
            className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            style={{
              background: `conic-gradient(from 0deg, ${colors.primary}20, ${colors.secondary}20, ${colors.primary}20)`,
              animation: isAnimating ? 'spin 0.6s ease-in-out' : 'none',
            }}
          />

          {/* Ripple effect on click */}
          <div
            className={`absolute inset-0 rounded-full transition-all duration-300 ${
              isAnimating ? 'scale-110 opacity-30' : 'scale-0 opacity-0'
            }`}
            style={{
              backgroundColor: colors.primary,
            }}
          />

          {/* Icon with enhanced animation */}
          <IconComponent
            className={`${iconSizeClasses[size]} transition-all duration-300 relative z-10 ${
              isAnimating ? 'rotate-180 scale-110' : 'group-hover:scale-105'
            }`}
          />

          {/* Subtle pulse ring */}
          <div
            className={`absolute inset-0 rounded-full transition-all duration-500 ${
              isAnimating ? 'scale-150 opacity-0' : 'scale-100 opacity-0'
            }`}
            style={{
              border: `2px solid ${colors.primary}40`,
              animation: isAnimating ? 'ping 0.6s ease-out' : 'none',
            }}
          />
        </button>

        {/* Label */}
        {showLabel && (
          <div className="absolute top-full mt-2 left-1/2 transform -translate-x-1/2">
            <span
              className="text-xs font-medium px-2 py-1 rounded-md whitespace-nowrap"
              style={{
                backgroundColor: `${colors.background}90`,
                color: colors.text,
                border: `1px solid ${colors.border}60`,
              }}
            >
              {currentTheme.label}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
