import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { CaptchaComponent } from './CaptchaComponent';

// Mock the theme store
vi.mock('../../stores/themeStore', () => ({
  useThemeStore: () => ({
    theme: 'light',
    colors: {
      primary: '#2563eb',
      error: '#dc2626',
      success: '#059669',
      background: '#ffffff',
      surface: '#f9fafb',
      text: '#111827',
      border: '#e5e7eb',
    },
  }),
}));

// Mock timers
vi.useFakeTimers();

describe('CaptchaComponent', () => {
  const defaultProps = {
    onVerify: vi.fn(),
    onExpire: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
  });

  it('renders captcha with math problem', () => {
    render(<CaptchaComponent {...defaultProps} />);

    expect(screen.getByText(/security check/i)).toBeInTheDocument();
    expect(screen.getByText(/what is the result of/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/your answer/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /verify/i })).toBeInTheDocument();
  });

  it('generates a math problem on mount', () => {
    render(<CaptchaComponent {...defaultProps} />);

    // Should show a math expression
    const mathExpression = screen.getByText(/[+\-×]/);
    expect(mathExpression).toBeInTheDocument();
  });

  it('verifies correct answer', async () => {
    const user = userEvent.setup();
    const onVerify = vi.fn();
    render(<CaptchaComponent {...defaultProps} onVerify={onVerify} />);

    // Find the math problem and calculate the answer
    const mathText = screen.getByText(/\d+\s*[+\-×]\s*\d+/).textContent;
    const answer = calculateMathAnswer(mathText!);

    const input = screen.getByPlaceholderText(/your answer/i);
    const verifyButton = screen.getByRole('button', { name: /verify/i });

    await user.type(input, answer.toString());
    await user.click(verifyButton);

    expect(onVerify).toHaveBeenCalledWith(expect.stringContaining('captcha_'));
    expect(screen.getByText(/verified/i)).toBeInTheDocument();
  });

  it('shows error for incorrect answer', async () => {
    const user = userEvent.setup();
    render(<CaptchaComponent {...defaultProps} />);

    const input = screen.getByPlaceholderText(/your answer/i);
    const verifyButton = screen.getByRole('button', { name: /verify/i });

    await user.type(input, '999'); // Incorrect answer
    await user.click(verifyButton);

    expect(screen.getByText(/incorrect answer/i)).toBeInTheDocument();
  });

  it('shows error for invalid input', async () => {
    const user = userEvent.setup();
    render(<CaptchaComponent {...defaultProps} />);

    const input = screen.getByPlaceholderText(/your answer/i);
    const verifyButton = screen.getByRole('button', { name: /verify/i });

    await user.type(input, 'abc'); // Non-numeric input
    await user.click(verifyButton);

    expect(
      screen.getByText(/please enter a valid number/i)
    ).toBeInTheDocument();
  });

  it('refreshes challenge when refresh button is clicked', async () => {
    const user = userEvent.setup();
    render(<CaptchaComponent {...defaultProps} />);

    const initialMath = screen.getByText(/\d+\s*[+\-×]\s*\d+/).textContent;
    const refreshButton = screen.getByLabelText(/refresh challenge/i);

    await user.click(refreshButton);

    const newMath = screen.getByText(/\d+\s*[+\-×]\s*\d+/).textContent;
    expect(newMath).not.toBe(initialMath);
  });

  it('expires after timeout', async () => {
    const onExpire = vi.fn();
    render(<CaptchaComponent {...defaultProps} onExpire={onExpire} />);

    // Fast-forward time to trigger expiration (5 minutes)
    vi.advanceTimersByTime(5 * 60 * 1000);

    await waitFor(() => {
      expect(onExpire).toHaveBeenCalled();
      expect(screen.getByText(/captcha expired/i)).toBeInTheDocument();
    });
  });

  it('allows regenerating challenge after expiration', async () => {
    const user = userEvent.setup();
    render(<CaptchaComponent {...defaultProps} />);

    // Trigger expiration
    vi.advanceTimersByTime(5 * 60 * 1000);

    await waitFor(() => {
      expect(screen.getByText(/captcha expired/i)).toBeInTheDocument();
    });

    const regenerateButton = screen.getByText(/generate new challenge/i);
    await user.click(regenerateButton);

    expect(screen.getByText(/security check/i)).toBeInTheDocument();
    expect(screen.queryByText(/captcha expired/i)).not.toBeInTheDocument();
  });

  it('tracks failed attempts', async () => {
    const user = userEvent.setup();
    render(<CaptchaComponent {...defaultProps} />);

    const input = screen.getByPlaceholderText(/your answer/i);
    const verifyButton = screen.getByRole('button', { name: /verify/i });

    // First failed attempt
    await user.clear(input);
    await user.type(input, '999');
    await user.click(verifyButton);

    expect(screen.getByText(/attempts: 1\/3/i)).toBeInTheDocument();

    // Second failed attempt
    await user.clear(input);
    await user.type(input, '998');
    await user.click(verifyButton);

    expect(screen.getByText(/attempts: 2\/3/i)).toBeInTheDocument();
  });

  it('generates new challenge after 3 failed attempts', async () => {
    const user = userEvent.setup();
    const onError = vi.fn();
    render(<CaptchaComponent {...defaultProps} onError={onError} />);

    const input = screen.getByPlaceholderText(/your answer/i);
    const verifyButton = screen.getByRole('button', { name: /verify/i });

    const initialMath = screen.getByText(/\d+\s*[+\-×]\s*\d+/).textContent;

    // Three failed attempts
    for (let i = 0; i < 3; i++) {
      await user.clear(input);
      await user.type(input, '999');
      await user.click(verifyButton);
    }

    expect(onError).toHaveBeenCalledWith(
      expect.stringContaining('Too many failed attempts')
    );

    // Should have new math problem
    const newMath = screen.getByText(/\d+\s*[+\-×]\s*\d+/).textContent;
    expect(newMath).not.toBe(initialMath);
  });

  it('disables verify button when input is empty', () => {
    render(<CaptchaComponent {...defaultProps} />);

    const verifyButton = screen.getByRole('button', { name: /verify/i });
    expect(verifyButton).toBeDisabled();
  });

  it('enables verify button when input has value', async () => {
    const user = userEvent.setup();
    render(<CaptchaComponent {...defaultProps} />);

    const input = screen.getByPlaceholderText(/your answer/i);
    const verifyButton = screen.getByRole('button', { name: /verify/i });

    await user.type(input, '5');
    expect(verifyButton).not.toBeDisabled();
  });

  it('supports keyboard interaction', async () => {
    const user = userEvent.setup();
    const onVerify = vi.fn();
    render(<CaptchaComponent {...defaultProps} onVerify={onVerify} />);

    const input = screen.getByPlaceholderText(/your answer/i);

    // Calculate correct answer
    const mathText = screen.getByText(/\d+\s*[+\-×]\s*\d+/).textContent;
    const answer = calculateMathAnswer(mathText!);

    await user.type(input, answer.toString());
    await user.keyboard('{Enter}');

    expect(onVerify).toHaveBeenCalled();
  });

  it('has proper accessibility attributes', () => {
    render(<CaptchaComponent {...defaultProps} />);

    const input = screen.getByPlaceholderText(/your answer/i);
    const refreshButton = screen.getByLabelText(/refresh challenge/i);

    expect(input).toHaveAttribute('type', 'number');
    expect(refreshButton).toHaveAttribute('aria-label');
    expect(refreshButton).toHaveAttribute('title');
  });
});

// Helper function to calculate math answer
function calculateMathAnswer(mathExpression: string): number {
  const match = mathExpression.match(/(\d+)\s*([+\-×])\s*(\d+)/);
  if (!match) return 0;

  const [, num1Str, operator, num2Str] = match;
  const num1 = parseInt(num1Str);
  const num2 = parseInt(num2Str);

  switch (operator) {
    case '+':
      return num1 + num2;
    case '-':
      return num1 - num2;
    case '×':
      return num1 * num2;
    default:
      return 0;
  }
}
