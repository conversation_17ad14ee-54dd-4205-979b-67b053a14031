<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Boundary Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            transition: background-color 0.3s ease;
        }
        .dark {
            background-color: #111827;
            color: #f9fafb;
        }
        .light {
            background-color: #ffffff;
            color: #111827;
        }
    </style>
</head>
<body class="light">
    <div id="root"></div>

    <script type="text/babel">
        // Mock error types and utilities for testing
        const ErrorSeverity = {
            LOW: 'low',
            MEDIUM: 'medium',
            HIGH: 'high',
            CRITICAL: 'critical'
        };

        const createAppError = (error, type, context) => ({
            ...error,
            type: type || 'UNKNOWN_ERROR',
            severity: ErrorSeverity.HIGH,
            timestamp: new Date(),
            userMessage: 'Something went wrong. Please try again.',
            context: context || {}
        });

        // Mock theme store
        const useThemeStore = () => {
            const [theme, setTheme] = React.useState('light');
            
            const lightColors = {
                primary: '#2563eb',
                secondary: '#4f46e5',
                text: '#111827',
                textSecondary: '#6b7280',
                background: '#ffffff',
                surface: '#f9fafb',
                border: '#e5e7eb',
                error: '#dc2626',
                warning: '#d97706',
                primaryForeground: '#ffffff',
                secondaryForeground: '#ffffff',
                ring: '#2563eb'
            };

            const darkColors = {
                primary: '#3b82f6',
                secondary: '#6366f1',
                text: '#f9fafb',
                textSecondary: '#d1d5db',
                background: '#111827',
                surface: '#1f2937',
                border: '#4b5563',
                error: '#ef4444',
                warning: '#f59e0b',
                primaryForeground: '#ffffff',
                secondaryForeground: '#ffffff',
                ring: '#3b82f6'
            };

            const colors = theme === 'dark' ? darkColors : lightColors;

            React.useEffect(() => {
                document.body.className = theme;
                document.body.style.backgroundColor = colors.background;
                document.body.style.color = colors.text;
            }, [theme, colors.background, colors.text]);

            return {
                theme,
                colors,
                setTheme: (newTheme) => {
                    setTheme(newTheme);
                }
            };
        };

        // Simple ThemeToggle component
        const ThemeToggle = () => {
            const { theme, setTheme, colors } = useThemeStore();
            
            const toggleTheme = () => {
                setTheme(theme === 'light' ? 'dark' : 'light');
            };

            return (
                <button
                    onClick={toggleTheme}
                    style={{
                        backgroundColor: colors.primary,
                        color: colors.primaryForeground,
                        border: 'none',
                        borderRadius: '8px',
                        padding: '8px 16px',
                        cursor: 'pointer',
                        fontSize: '14px',
                        fontWeight: '500'
                    }}
                >
                    {theme === 'light' ? '🌙 Dark' : '☀️ Light'}
                </button>
            );
        };

        // Mock recovery strategies
        const getRecoveryStrategies = () => [
            {
                id: 'retry',
                label: 'Try Again',
                icon: '🔄',
                primary: true,
                description: 'Retry the failed operation'
            },
            {
                id: 'reload',
                label: 'Reload Page',
                icon: '↻',
                primary: false,
                description: 'Reload the entire page'
            }
        ];

        const executeRecoveryStrategy = async (strategy) => {
            if (strategy.id === 'reload') {
                window.location.reload();
            }
            // For demo, just resolve
            return Promise.resolve();
        };

        const reportError = async () => Promise.resolve();

        // Simplified ErrorFallback component
        const ErrorFallback = ({ error, resetError }) => {
            const { colors, theme } = useThemeStore();
            const [recoveryStrategies] = React.useState(getRecoveryStrategies());

            const getSeverityStyles = () => ({
                borderColor: colors.error,
                backgroundColor: `${colors.error}10`,
                iconColor: colors.error
            });

            const severityStyles = getSeverityStyles();

            return (
                <div
                    style={{
                        border: `1px solid ${severityStyles.borderColor}`,
                        backgroundColor: severityStyles.backgroundColor,
                        borderRadius: '8px',
                        padding: '24px',
                        maxWidth: '600px',
                        margin: '0 auto'
                    }}
                >
                    {/* Theme Toggle */}
                    <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '16px' }}>
                        <ThemeToggle />
                    </div>

                    {/* Error Header */}
                    <div style={{ textAlign: 'center', marginBottom: '24px' }}>
                        <div style={{ fontSize: '48px', marginBottom: '16px', color: severityStyles.iconColor }}>
                            ❌
                        </div>
                        <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '8px', color: colors.text }}>
                            Component Error
                        </h2>
                        <p style={{ fontSize: '16px', marginBottom: '16px', color: colors.textSecondary }}>
                            {error.userMessage || 'An unexpected error occurred'}
                        </p>
                    </div>

                    {/* Recovery Actions */}
                    <div style={{ marginBottom: '24px' }}>
                        <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '12px', textAlign: 'center', color: colors.text }}>
                            What would you like to do?
                        </h3>
                        <div style={{ display: 'flex', gap: '12px', justifyContent: 'center', flexWrap: 'wrap' }}>
                            {recoveryStrategies.map((strategy, index) => (
                                <button
                                    key={index}
                                    onClick={() => strategy.id === 'retry' ? resetError() : executeRecoveryStrategy(strategy)}
                                    style={{
                                        backgroundColor: strategy.primary ? colors.primary : colors.secondary,
                                        color: strategy.primary ? colors.primaryForeground : colors.secondaryForeground,
                                        border: 'none',
                                        borderRadius: '8px',
                                        padding: '8px 16px',
                                        cursor: 'pointer',
                                        fontSize: '14px',
                                        fontWeight: '500',
                                        boxShadow: strategy.primary ? `0 0 0 2px ${colors.ring}` : 'none'
                                    }}
                                >
                                    <span style={{ marginRight: '8px' }}>{strategy.icon}</span>
                                    {strategy.label}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Technical Details */}
                    <div
                        style={{
                            backgroundColor: theme === 'dark' ? '#1f2937' : '#f9fafb',
                            border: `1px solid ${colors.border}`,
                            borderRadius: '8px',
                            padding: '16px',
                            fontSize: '14px',
                            fontFamily: 'monospace',
                            color: colors.text
                        }}
                    >
                        <div><strong>Type:</strong> {error.type}</div>
                        <div><strong>Message:</strong> {error.message}</div>
                        <div><strong>Timestamp:</strong> {error.timestamp.toLocaleString()}</div>
                    </div>
                </div>
            );
        };

        // Component that throws an error
        const BuggyComponent = () => {
            const [shouldThrow, setShouldThrow] = React.useState(false);

            if (shouldThrow) {
                throw new Error('This is a test error!');
            }

            return (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                    <h1>Error Boundary Test</h1>
                    <p>Click the button below to trigger an error and see the error boundary in action:</p>
                    <button
                        onClick={() => setShouldThrow(true)}
                        style={{
                            backgroundColor: '#dc2626',
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            padding: '12px 24px',
                            cursor: 'pointer',
                            fontSize: '16px',
                            fontWeight: '500',
                            marginTop: '20px'
                        }}
                    >
                        Trigger Error
                    </button>
                </div>
            );
        };

        // Simple Error Boundary
        class ErrorBoundary extends React.Component {
            constructor(props) {
                super(props);
                this.state = { hasError: false, error: null };
            }

            static getDerivedStateFromError(error) {
                const appError = createAppError(error);
                return { hasError: true, error: appError };
            }

            componentDidCatch(error, errorInfo) {
                console.error('Error caught by boundary:', error, errorInfo);
            }

            render() {
                if (this.state.hasError) {
                    return (
                        <ErrorFallback
                            error={this.state.error}
                            resetError={() => this.setState({ hasError: false, error: null })}
                        />
                    );
                }

                return this.props.children;
            }
        }

        // Main App
        const App = () => {
            return (
                <div style={{ padding: '20px', minHeight: '100vh' }}>
                    <ErrorBoundary>
                        <BuggyComponent />
                    </ErrorBoundary>
                </div>
            );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>