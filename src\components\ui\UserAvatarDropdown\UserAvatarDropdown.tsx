import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import Dropdown from '../Dropdown/Dropdown';
import { 
  SettingsIcon, 
  LogOutIcon, 
  BookIcon, 
  KeyboardIcon, 
  DownloadIcon, 
  PlayIcon 
} from '../../icons';
import type { DropdownItem } from '../Dropdown/Dropdown';

export interface UserAvatarDropdownProps {
  user: {
    name: string;
    avatar: React.ReactNode;
    email?: string;
  };
  onPreferences?: () => void;
  onLogout?: () => void;
  onDocumentation?: () => void;
  onShortcuts?: () => void;
  onInstallApp?: () => void;
  onOnboarding?: () => void;
  className?: string;
  'data-testid'?: string;
}

const UserAvatarDropdown: React.FC<UserAvatarDropdownProps> = ({
  user,
  onPreferences,
  onLogout,
  onDocumentation,
  onShortcuts,
  onInstallApp,
  onOnboarding,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const dropdownItems: DropdownItem[] = [
    {
      id: 'preferences',
      label: 'Preferences',
      icon: <SettingsIcon className="w-4 h-4" />,
      onClick: onPreferences || (() => console.log('Open preferences')),
      description: 'Account and app settings',
    },
    {
      id: 'divider-1',
      label: '',
      isDivider: true,
    },
    {
      id: 'documentation',
      label: 'Documentation',
      icon: <BookIcon className="w-4 h-4" />,
      onClick: onDocumentation || (() => console.log('Open documentation')),
      description: 'Help and guides',
    },
    {
      id: 'shortcuts',
      label: 'Shortcuts',
      icon: <KeyboardIcon className="w-4 h-4" />,
      onClick: onShortcuts || (() => console.log('Show shortcuts')),
      shortcut: 'Ctrl+K',
      description: 'Keyboard shortcuts',
    },
    {
      id: 'divider-2',
      label: '',
      isDivider: true,
    },
    {
      id: 'install-app',
      label: 'Install App',
      icon: <DownloadIcon className="w-4 h-4" />,
      onClick: onInstallApp || (() => console.log('Install app')),
      description: 'Install as PWA',
    },
    {
      id: 'onboarding',
      label: 'Onboarding / Tour',
      icon: <PlayIcon className="w-4 h-4" />,
      onClick: onOnboarding || (() => console.log('Start onboarding')),
      description: 'Take a guided tour',
    },
    {
      id: 'divider-3',
      label: '',
      isDivider: true,
    },
    {
      id: 'logout',
      label: 'Logout',
      icon: <LogOutIcon className="w-4 h-4" />,
      onClick: onLogout || (() => console.log('Logout')),
    },
  ];

  const trigger = (
    <div
      className={`flex items-center space-x-2 p-1 rounded-full hover:bg-opacity-10 transition-colors duration-200 cursor-pointer ${className}`}
      style={{ backgroundColor: 'transparent' }}
    >
      {/* User Avatar */}
      <div className="w-8 h-8 rounded-full overflow-hidden flex items-center justify-center">
        {user.avatar}
      </div>
    </div>
  );

  return (
    <Dropdown
      trigger={trigger}
      items={dropdownItems}
      align="right"
      className={className}
      dropdownClassName="min-w-[240px]"
      data-testid={testId}
    />
  );
};

export default UserAvatarDropdown;
